import customtkinter as ctk
import json
import os
from typing import Dict, List, Optional

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class CodeTrainingApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Code Training App - 60 Days Challenge")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Initialize data
        self.languages = [
            "Python", "JavaScript", "Java", "C++", "C#", "Go", 
            "Rust", "TypeScript", "PHP", "Ruby", "Swift", "Kotlin"
        ]
        self.selected_language = ctk.StringVar(value="Python")
        self.selected_day = ctk.IntVar(value=1)
        self.completed_quests = self.load_progress()
        
        # Initialize quest data
        self.quest_data = self.initialize_quest_data()
        
        self.setup_ui()
        
    def load_progress(self) -> Dict[str, List[int]]:
        """Load progress from JSON file"""
        try:
            if os.path.exists("progress.json"):
                with open("progress.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {lang: [] for lang in self.languages}
    
    def save_progress(self):
        """Save progress to JSON file"""
        try:
            with open("progress.json", "w") as f:
                json.dump(self.completed_quests, f, indent=2)
        except Exception as e:
            print(f"Error saving progress: {e}")
    
    def initialize_quest_data(self) -> Dict[str, Dict[int, Dict]]:
        """Initialize quest data for all languages and days"""
        quest_data = {}
        
        for language in self.languages:
            quest_data[language] = {}
            for day in range(1, 61):
                quest_data[language][day] = self.generate_quest_for_day(language, day)
        
        return quest_data
    
    def generate_quest_for_day(self, language: str, day: int) -> Dict:
        """Generate quest content for a specific language and day"""
        # This will be expanded with actual quest content
        difficulty_levels = ["Beginner", "Intermediate", "Advanced"]
        
        if day <= 20:
            difficulty = "Beginner"
        elif day <= 40:
            difficulty = "Intermediate"
        else:
            difficulty = "Advanced"
            
        # Basic quest structure - will be expanded
        quest = {
            "title": f"Day {day}: {language} Challenge",
            "difficulty": difficulty,
            "description": f"Learn {language} fundamentals and practice coding skills.",
            "objectives": [
                f"Complete {language} exercises",
                "Practice problem-solving",
                "Build a small project"
            ],
            "estimated_time": "30-60 minutes"
        }
        
        return quest
    
    def setup_ui(self):
        """Set up the main user interface"""
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🚀 Code Training App - 60 Days Challenge",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # Content area
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        self.setup_selection_panel()
        self.setup_quest_display()
        
    def setup_selection_panel(self):
        """Set up language and day selection panel"""
        # Selection panel
        selection_frame = ctk.CTkFrame(self.content_frame)
        selection_frame.pack(fill="x", padx=20, pady=20)
        
        # Language selection
        lang_frame = ctk.CTkFrame(selection_frame)
        lang_frame.pack(side="left", fill="both", expand=True, padx=(20, 10), pady=20)
        
        ctk.CTkLabel(
            lang_frame, 
            text="Select Programming Language:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        self.language_dropdown = ctk.CTkOptionMenu(
            lang_frame,
            variable=self.selected_language,
            values=self.languages,
            command=self.on_language_change
        )
        self.language_dropdown.pack(pady=(5, 10))
        
        # Day selection
        day_frame = ctk.CTkFrame(selection_frame)
        day_frame.pack(side="right", fill="both", expand=True, padx=(10, 20), pady=20)
        
        ctk.CTkLabel(
            day_frame, 
            text="Select Day (1-60):",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        self.day_slider = ctk.CTkSlider(
            day_frame,
            from_=1,
            to=60,
            number_of_steps=59,
            variable=self.selected_day,
            command=self.on_day_change
        )
        self.day_slider.pack(pady=5, padx=20, fill="x")
        
        self.day_label = ctk.CTkLabel(day_frame, text="Day: 1")
        self.day_label.pack(pady=(5, 10))
        
    def setup_quest_display(self):
        """Set up quest display area"""
        # Quest display frame
        self.quest_frame = ctk.CTkScrollableFrame(self.content_frame)
        self.quest_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        self.update_quest_display()
        
    def on_language_change(self, value):
        """Handle language selection change"""
        self.update_quest_display()
        
    def on_day_change(self, value):
        """Handle day selection change"""
        day = int(value)
        self.day_label.configure(text=f"Day: {day}")
        self.update_quest_display()
        
    def update_quest_display(self):
        """Update the quest display with current selection"""
        # Clear existing content
        for widget in self.quest_frame.winfo_children():
            widget.destroy()
            
        language = self.selected_language.get()
        day = self.selected_day.get()
        quest = self.quest_data[language][day]
        
        # Quest title
        title_label = ctk.CTkLabel(
            self.quest_frame,
            text=quest["title"],
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # Difficulty and time
        info_frame = ctk.CTkFrame(self.quest_frame)
        info_frame.pack(fill="x", pady=10, padx=20)
        
        difficulty_label = ctk.CTkLabel(
            info_frame,
            text=f"Difficulty: {quest['difficulty']}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        difficulty_label.pack(side="left", padx=20, pady=10)
        
        time_label = ctk.CTkLabel(
            info_frame,
            text=f"Estimated Time: {quest['estimated_time']}",
            font=ctk.CTkFont(size=14)
        )
        time_label.pack(side="right", padx=20, pady=10)
        
        # Description
        desc_label = ctk.CTkLabel(
            self.quest_frame,
            text=quest["description"],
            font=ctk.CTkFont(size=16),
            wraplength=800
        )
        desc_label.pack(pady=10, padx=20)
        
        # Objectives
        obj_label = ctk.CTkLabel(
            self.quest_frame,
            text="Learning Objectives:",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        obj_label.pack(pady=(20, 10))
        
        for obj in quest["objectives"]:
            obj_item = ctk.CTkLabel(
                self.quest_frame,
                text=f"• {obj}",
                font=ctk.CTkFont(size=14)
            )
            obj_item.pack(anchor="w", padx=40, pady=2)
        
        # Complete button
        is_completed = day in self.completed_quests.get(language, [])
        button_text = "✓ Completed" if is_completed else "Mark as Complete"
        button_color = "green" if is_completed else "blue"
        
        self.complete_button = ctk.CTkButton(
            self.quest_frame,
            text=button_text,
            command=self.toggle_quest_completion,
            fg_color=button_color,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.complete_button.pack(pady=20)
        
    def toggle_quest_completion(self):
        """Toggle quest completion status"""
        language = self.selected_language.get()
        day = self.selected_day.get()
        
        if language not in self.completed_quests:
            self.completed_quests[language] = []
            
        if day in self.completed_quests[language]:
            self.completed_quests[language].remove(day)
        else:
            self.completed_quests[language].append(day)
            
        self.save_progress()
        self.update_quest_display()
        
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CodeTrainingApp()
    app.run()
