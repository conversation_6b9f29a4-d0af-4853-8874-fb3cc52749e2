import customtkinter as ctk
import json
import os
import time
import threading
import webbrowser
from typing import Dict, List, Optional

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class CodeTrainingApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Code Training App - 60 Days Challenge")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)

        # Initialize data
        self.languages = [
            "Python", "JavaScript", "Java", "C++", "C#", "Go",
            "Rust", "TypeScript", "PHP", "Ruby", "Swift", "Kotlin"
        ]
        self.selected_language = ctk.StringVar(value="Python")
        self.selected_day = ctk.IntVar(value=1)
        self.completed_quests = self.load_progress()

        # Timer variables
        self.timer_running = False
        self.timer_seconds = 0
        self.timer_thread = None

        # Initialize quest data
        self.quest_data = self.initialize_comprehensive_quest_data()

        self.setup_ui()
        
    def load_progress(self) -> Dict[str, List[int]]:
        """Load progress from JSON file"""
        try:
            if os.path.exists("progress.json"):
                with open("progress.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {lang: [] for lang in self.languages}
    
    def save_progress(self):
        """Save progress to JSON file"""
        try:
            with open("progress.json", "w") as f:
                json.dump(self.completed_quests, f, indent=2)
        except Exception as e:
            print(f"Error saving progress: {e}")
    
    def initialize_comprehensive_quest_data(self) -> Dict[str, Dict[int, Dict]]:
        """Initialize comprehensive quest data for all languages and days"""
        quest_data = {}

        for language in self.languages:
            quest_data[language] = {}
            for day in range(1, 61):
                quest_data[language][day] = self.generate_detailed_quest(language, day)

        return quest_data

    def generate_detailed_quest(self, language: str, day: int) -> Dict:
        """Generate detailed quest content for a specific language and day"""

        # Define quest templates for different languages and progression
        quest_templates = {
            "Python": self.get_python_quests(),
            "JavaScript": self.get_javascript_quests(),
            "Java": self.get_java_quests(),
            "C++": self.get_cpp_quests(),
            "C#": self.get_csharp_quests(),
            "Go": self.get_go_quests(),
            "Rust": self.get_rust_quests(),
            "TypeScript": self.get_typescript_quests(),
            "PHP": self.get_php_quests(),
            "Ruby": self.get_ruby_quests(),
            "Swift": self.get_swift_quests(),
            "Kotlin": self.get_kotlin_quests()
        }

        # Get the appropriate quest for the day
        quests = quest_templates.get(language, self.get_generic_quests())
        quest_index = (day - 1) % len(quests)
        base_quest = quests[quest_index].copy()

        # Adjust difficulty based on day
        if day <= 20:
            difficulty = "Beginner"
            difficulty_emoji = "🟢"
        elif day <= 40:
            difficulty = "Intermediate"
            difficulty_emoji = "🟡"
        else:
            difficulty = "Advanced"
            difficulty_emoji = "🔴"

        base_quest["day"] = day
        base_quest["difficulty"] = f"{difficulty_emoji} {difficulty}"
        base_quest["title"] = f"Tag {day}: {base_quest['title']}"

        return base_quest

    def get_python_quests(self) -> List[Dict]:
        """Get Python-specific quest data"""
        return [
            {
                "title": "Python Grundlagen - Variablen & Datentypen",
                "description": "Lerne die Grundlagen von Python: Variablen, Strings, Zahlen und Listen.",
                "objectives": [
                    "Erstelle Variablen verschiedener Datentypen",
                    "Arbeite mit Strings und String-Methoden",
                    "Verwende Listen und Tupel",
                    "Führe einfache Berechnungen durch"
                ],
                "code_example": '''# Variablen und Datentypen
name = "Max Mustermann"
alter = 25
hobbies = ["Programmieren", "Lesen", "Sport"]

print(f"Hallo {name}, du bist {alter} Jahre alt!")
print(f"Deine Hobbies: {', '.join(hobbies)}")''',
                "exercise": "Erstelle ein Programm, das Benutzerdaten sammelt und formatiert ausgibt.",
                "youtube_links": [
                    "https://youtube.com/watch?v=python-basics",
                    "https://youtube.com/watch?v=python-variables"
                ]
            },
            {
                "title": "Kontrollstrukturen - If/Else & Schleifen",
                "description": "Lerne Entscheidungen zu treffen und Code zu wiederholen.",
                "objectives": [
                    "Verwende if/elif/else Anweisungen",
                    "Schreibe for- und while-Schleifen",
                    "Kombiniere Bedingungen mit and/or",
                    "Arbeite mit range() und enumerate()"
                ],
                "code_example": '''# Kontrollstrukturen
zahlen = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

for zahl in zahlen:
    if zahl % 2 == 0:
        print(f"{zahl} ist gerade")
    else:
        print(f"{zahl} ist ungerade")''',
                "exercise": "Erstelle ein Zahlenratespiel mit Schleifen und Bedingungen.",
                "youtube_links": [
                    "https://youtube.com/watch?v=python-loops",
                    "https://youtube.com/watch?v=python-conditions"
                ]
            },
            {
                "title": "Funktionen & Module",
                "description": "Organisiere deinen Code mit Funktionen und importiere Module.",
                "objectives": [
                    "Definiere eigene Funktionen",
                    "Verwende Parameter und Rückgabewerte",
                    "Importiere und nutze Module",
                    "Verstehe Scope und lokale/globale Variablen"
                ],
                "code_example": '''# Funktionen
def berechne_kreis_flaeche(radius):
    import math
    return math.pi * radius ** 2

def begruessung(name, alter=18):
    return f"Hallo {name}, du bist {alter} Jahre alt!"

# Funktionen aufrufen
flaeche = berechne_kreis_flaeche(5)
nachricht = begruessung("Anna", 25)
print(nachricht)
print(f"Kreisfläche: {flaeche:.2f}")''',
                "exercise": "Erstelle einen Taschenrechner mit verschiedenen Funktionen.",
                "youtube_links": [
                    "https://youtube.com/watch?v=python-functions",
                    "https://youtube.com/watch?v=python-modules"
                ]
            },
            {
                "title": "Listen & Dictionaries",
                "description": "Arbeite mit komplexen Datenstrukturen in Python.",
                "objectives": [
                    "Verstehe Listen-Methoden (append, remove, sort)",
                    "Arbeite mit Dictionaries und Keys",
                    "Verwende List Comprehensions",
                    "Iteriere über Datenstrukturen"
                ],
                "code_example": '''# Listen und Dictionaries
studenten = [
    {"name": "Anna", "note": 1.5, "fach": "Informatik"},
    {"name": "Max", "note": 2.0, "fach": "Mathematik"},
    {"name": "Lisa", "note": 1.8, "fach": "Physik"}
]

# List Comprehension
gute_noten = [s["name"] for s in studenten if s["note"] <= 1.8]
print("Studenten mit guten Noten:", gute_noten)

# Dictionary Operationen
noten_dict = {s["name"]: s["note"] for s in studenten}
print("Noten Dictionary:", noten_dict)''',
                "exercise": "Erstelle ein Adressbuch mit Such- und Sortierfunktionen.",
                "youtube_links": [
                    "https://youtube.com/watch?v=python-lists",
                    "https://youtube.com/watch?v=python-dictionaries"
                ]
            },
            {
                "title": "Datei-Operationen & Exception Handling",
                "description": "Lerne Dateien zu lesen/schreiben und Fehler zu behandeln.",
                "objectives": [
                    "Öffne und schließe Dateien sicher",
                    "Lese und schreibe Text-/CSV-Dateien",
                    "Verwende try/except für Fehlerbehandlung",
                    "Arbeite mit dem 'with' Statement"
                ],
                "code_example": '''# Datei-Operationen mit Exception Handling
import csv

def speichere_daten(dateiname, daten):
    try:
        with open(dateiname, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(['Name', 'Alter', 'Stadt'])
            for person in daten:
                writer.writerow(person)
        print(f"Daten erfolgreich in {dateiname} gespeichert!")
    except Exception as e:
        print(f"Fehler beim Speichern: {e}")

# Beispiel-Daten
personen = [
    ['Anna', 25, 'Berlin'],
    ['Max', 30, 'München'],
    ['Lisa', 28, 'Hamburg']
]

speichere_daten('personen.csv', personen)''',
                "exercise": "Erstelle ein Programm zum Verwalten einer Kontaktliste in CSV-Format.",
                "youtube_links": [
                    "https://youtube.com/watch?v=python-files",
                    "https://youtube.com/watch?v=python-exceptions"
                ]
            }
        ]

    def get_javascript_quests(self) -> List[Dict]:
        """Get JavaScript-specific quest data"""
        return [
            {
                "title": "JavaScript Grundlagen - DOM Manipulation",
                "description": "Lerne JavaScript Grundlagen und wie man HTML-Elemente manipuliert.",
                "objectives": [
                    "Verstehe Variablen (let, const, var)",
                    "Arbeite mit dem DOM",
                    "Füge Event Listener hinzu",
                    "Ändere HTML-Inhalte dynamisch"
                ],
                "code_example": '''// DOM Manipulation
const button = document.getElementById('myButton');
const output = document.getElementById('output');

button.addEventListener('click', function() {
    const name = prompt('Wie heißt du?');
    output.innerHTML = `<h2>Hallo ${name}!</h2>`;
    output.style.color = 'blue';
});''',
                "exercise": "Erstelle eine interaktive Webseite mit Buttons und dynamischen Inhalten.",
                "youtube_links": [
                    "https://youtube.com/watch?v=javascript-dom",
                    "https://youtube.com/watch?v=javascript-events"
                ]
            },
            {
                "title": "Arrays & Objekte",
                "description": "Arbeite mit komplexen Datenstrukturen in JavaScript.",
                "objectives": [
                    "Erstelle und manipuliere Arrays",
                    "Verwende Array-Methoden (map, filter, reduce)",
                    "Arbeite mit Objekten und Properties",
                    "Verstehe JSON und Datenkonvertierung"
                ],
                "code_example": '''// Arrays und Objekte
const studenten = [
    {name: 'Anna', note: 1.5, fach: 'Informatik'},
    {name: 'Max', note: 2.0, fach: 'Mathematik'},
    {name: 'Lisa', note: 1.8, fach: 'Physik'}
];

const besteStudenten = studenten
    .filter(student => student.note <= 1.8)
    .map(student => student.name);

console.log('Beste Studenten:', besteStudenten);''',
                "exercise": "Erstelle eine Studentenverwaltung mit Filterung und Sortierung.",
                "youtube_links": [
                    "https://youtube.com/watch?v=javascript-arrays",
                    "https://youtube.com/watch?v=javascript-objects"
                ]
            }
        ]

    def get_java_quests(self) -> List[Dict]:
        """Get Java-specific quest data"""
        return [
            {
                "title": "Java Grundlagen - Klassen & Objekte",
                "description": "Lerne objektorientierte Programmierung mit Java.",
                "objectives": [
                    "Erstelle Klassen und Objekte",
                    "Verwende Konstruktoren und Methoden",
                    "Verstehe Vererbung und Polymorphismus",
                    "Arbeite mit Packages und Imports"
                ],
                "code_example": '''// Java Klassen
public class Auto {
    private String marke;
    private int baujahr;

    public Auto(String marke, int baujahr) {
        this.marke = marke;
        this.baujahr = baujahr;
    }

    public void fahren() {
        System.out.println(marke + " fährt!");
    }

    public int getAlter() {
        return 2024 - baujahr;
    }
}''',
                "exercise": "Erstelle ein Klassensystem für eine Fahrzeugverwaltung.",
                "youtube_links": [
                    "https://youtube.com/watch?v=java-oop",
                    "https://youtube.com/watch?v=java-classes"
                ]
            }
        ]

    def get_generic_quests(self) -> List[Dict]:
        """Get generic quest data for languages without specific content"""
        return [
            {
                "title": "Grundlagen der Programmierung",
                "description": "Lerne die fundamentalen Konzepte dieser Programmiersprache.",
                "objectives": [
                    "Verstehe Syntax und Grundlagen",
                    "Arbeite mit Variablen und Datentypen",
                    "Verwende Kontrollstrukturen",
                    "Schreibe einfache Programme"
                ],
                "code_example": "// Beispielcode wird hier angezeigt",
                "exercise": "Erstelle ein einfaches Programm mit den gelernten Konzepten.",
                "youtube_links": ["https://youtube.com/programming-basics"]
            }
        ]

    # Placeholder methods for other languages (similar structure)
    def get_cpp_quests(self): return self.get_generic_quests()
    def get_csharp_quests(self): return self.get_generic_quests()
    def get_go_quests(self): return self.get_generic_quests()
    def get_rust_quests(self): return self.get_generic_quests()
    def get_typescript_quests(self): return self.get_generic_quests()
    def get_php_quests(self): return self.get_generic_quests()
    def get_ruby_quests(self): return self.get_generic_quests()
    def get_swift_quests(self): return self.get_generic_quests()
    def get_kotlin_quests(self): return self.get_generic_quests()

    def setup_ui(self):
        """Set up the main user interface based on the provided layout"""
        # Configure grid weights
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(1, weight=1)

        # Top header with timer
        self.setup_header()

        # Left sidebar with day navigation
        self.setup_sidebar()

        # Main content area
        self.setup_main_content()

        # Bottom bar with settings and links
        self.setup_bottom_bar()

        # Start timer update
        self.update_timer_display()

    def setup_header(self):
        """Set up the top header with timer"""
        header_frame = ctk.CTkFrame(self.root, height=60)
        header_frame.grid(row=0, column=0, columnspan=3, sticky="ew", padx=10, pady=(10, 5))
        header_frame.grid_propagate(False)

        # Timer display
        self.timer_label = ctk.CTkLabel(
            header_frame,
            text="Timer 00:00",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.timer_label.pack(side="left", padx=20, pady=15)

        # Clock display
        self.clock_label = ctk.CTkLabel(
            header_frame,
            text="Uhr",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.clock_label.pack(side="right", padx=20, pady=15)

        # Timer control buttons
        timer_frame = ctk.CTkFrame(header_frame)
        timer_frame.pack(side="left", padx=20)

        self.start_timer_btn = ctk.CTkButton(
            timer_frame,
            text="▶️",
            width=40,
            command=self.toggle_timer
        )
        self.start_timer_btn.pack(side="left", padx=2)

        self.reset_timer_btn = ctk.CTkButton(
            timer_frame,
            text="🔄",
            width=40,
            command=self.reset_timer
        )
        self.reset_timer_btn.pack(side="left", padx=2)

    def setup_sidebar(self):
        """Set up the left sidebar with day navigation"""
        sidebar_frame = ctk.CTkFrame(self.root, width=200)
        sidebar_frame.grid(row=1, column=0, sticky="nsew", padx=(10, 5), pady=5)
        sidebar_frame.grid_propagate(False)

        # Language selection at top of sidebar
        lang_label = ctk.CTkLabel(
            sidebar_frame,
            text="Sprache:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        lang_label.pack(pady=(15, 5))

        self.language_dropdown = ctk.CTkOptionMenu(
            sidebar_frame,
            variable=self.selected_language,
            values=self.languages,
            command=self.on_language_change
        )
        self.language_dropdown.pack(pady=(0, 15), padx=10, fill="x")

        # Day navigation
        days_label = ctk.CTkLabel(
            sidebar_frame,
            text="Tage:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        days_label.pack(pady=(10, 5))

        # Scrollable frame for day buttons
        self.days_scroll_frame = ctk.CTkScrollableFrame(sidebar_frame, height=400)
        self.days_scroll_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # Create day buttons
        self.day_buttons = []
        for day in range(1, 61):
            # Add emoji for different difficulty levels
            if day <= 20:
                emoji = "🟢"
            elif day <= 40:
                emoji = "🟡"
            else:
                emoji = "🔴"

            btn = ctk.CTkButton(
                self.days_scroll_frame,
                text=f"{emoji} Tag {day}",
                width=160,
                height=35,
                command=lambda d=day: self.select_day(d)
            )
            btn.pack(pady=2, fill="x")
            self.day_buttons.append(btn)

        # Highlight first day
        self.update_day_button_colors()

    def setup_main_content(self):
        """Set up the main content area"""
        self.main_content_frame = ctk.CTkScrollableFrame(self.root)
        self.main_content_frame.grid(row=1, column=1, sticky="nsew", padx=5, pady=5)

        self.update_quest_display()

    def setup_bottom_bar(self):
        """Set up the bottom bar with settings and links"""
        bottom_frame = ctk.CTkFrame(self.root, height=60)
        bottom_frame.grid(row=2, column=0, columnspan=3, sticky="ew", padx=10, pady=(5, 10))
        bottom_frame.grid_propagate(False)

        # Settings button (left)
        settings_btn = ctk.CTkButton(
            bottom_frame,
            text="Settings",
            width=100,
            command=self.open_settings
        )
        settings_btn.pack(side="left", padx=20, pady=15)

        # Right side buttons
        right_frame = ctk.CTkFrame(bottom_frame)
        right_frame.pack(side="right", padx=20, pady=10)

        # Edit button
        edit_btn = ctk.CTkButton(
            right_frame,
            text="Bearbeiten",
            width=100,
            command=self.open_editor
        )
        edit_btn.pack(side="left", padx=5)

        # YouTube links button
        youtube_btn = ctk.CTkButton(
            right_frame,
            text="📺 YouTube Links",
            width=140,
            command=self.open_youtube_links
        )
        youtube_btn.pack(side="left", padx=5)

    def select_day(self, day: int):
        """Select a specific day"""
        self.selected_day.set(day)
        self.update_day_button_colors()
        self.update_quest_display()

    def update_day_button_colors(self):
        """Update day button colors based on completion status"""
        current_day = self.selected_day.get()
        language = self.selected_language.get()
        completed_days = self.completed_quests.get(language, [])

        for i, btn in enumerate(self.day_buttons):
            day = i + 1
            if day == current_day:
                btn.configure(fg_color=("#3B8ED0", "#1F6AA5"))  # Selected
            elif day in completed_days:
                btn.configure(fg_color=("#2CC985", "#2FA572"))  # Completed (green)
            else:
                btn.configure(fg_color=("#1F6AA5", "#144870"))  # Default

    def on_language_change(self, value):
        """Handle language selection change"""
        self.update_day_button_colors()
        self.update_quest_display()

    def update_quest_display(self):
        """Update the quest display with current selection"""
        # Clear existing content
        for widget in self.main_content_frame.winfo_children():
            widget.destroy()

        language = self.selected_language.get()
        day = self.selected_day.get()
        quest = self.quest_data[language][day]

        # Quest title
        title_label = ctk.CTkLabel(
            self.main_content_frame,
            text=quest["title"],
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(20, 15))

        # Difficulty badge
        difficulty_frame = ctk.CTkFrame(self.main_content_frame)
        difficulty_frame.pack(pady=10)

        difficulty_label = ctk.CTkLabel(
            difficulty_frame,
            text=quest["difficulty"],
            font=ctk.CTkFont(size=16, weight="bold")
        )
        difficulty_label.pack(padx=20, pady=10)

        # Description
        desc_label = ctk.CTkLabel(
            self.main_content_frame,
            text=quest["description"],
            font=ctk.CTkFont(size=16),
            wraplength=700
        )
        desc_label.pack(pady=15, padx=20)

        # Objectives section
        obj_frame = ctk.CTkFrame(self.main_content_frame)
        obj_frame.pack(fill="x", pady=15, padx=20)

        obj_title = ctk.CTkLabel(
            obj_frame,
            text="🎯 Lernziele:",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        obj_title.pack(pady=(15, 10))

        for obj in quest["objectives"]:
            obj_item = ctk.CTkLabel(
                obj_frame,
                text=f"• {obj}",
                font=ctk.CTkFont(size=14),
                anchor="w"
            )
            obj_item.pack(anchor="w", padx=30, pady=3, fill="x")

        # Code example section
        if "code_example" in quest:
            code_frame = ctk.CTkFrame(self.main_content_frame)
            code_frame.pack(fill="x", pady=15, padx=20)

            code_title = ctk.CTkLabel(
                code_frame,
                text="💻 Code Beispiel:",
                font=ctk.CTkFont(size=18, weight="bold")
            )
            code_title.pack(pady=(15, 10))

            code_text = ctk.CTkTextbox(
                code_frame,
                height=200,
                font=ctk.CTkFont(family="Courier", size=12)
            )
            code_text.pack(fill="x", padx=20, pady=(0, 15))
            code_text.insert("1.0", quest["code_example"])
            code_text.configure(state="disabled")

        # Exercise section
        if "exercise" in quest:
            exercise_frame = ctk.CTkFrame(self.main_content_frame)
            exercise_frame.pack(fill="x", pady=15, padx=20)

            exercise_title = ctk.CTkLabel(
                exercise_frame,
                text="📝 Aufgabe:",
                font=ctk.CTkFont(size=18, weight="bold")
            )
            exercise_title.pack(pady=(15, 10))

            exercise_label = ctk.CTkLabel(
                exercise_frame,
                text=quest["exercise"],
                font=ctk.CTkFont(size=14),
                wraplength=650
            )
            exercise_label.pack(pady=(0, 15), padx=20)

        # Complete button
        is_completed = day in self.completed_quests.get(language, [])
        button_text = "✅ Abgeschlossen" if is_completed else "Als abgeschlossen markieren"
        button_color = ("#2CC985", "#2FA572") if is_completed else ("#3B8ED0", "#1F6AA5")

        self.complete_button = ctk.CTkButton(
            self.main_content_frame,
            text=button_text,
            command=self.toggle_quest_completion,
            fg_color=button_color,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40
        )
        self.complete_button.pack(pady=20)

    def toggle_quest_completion(self):
        """Toggle quest completion status"""
        language = self.selected_language.get()
        day = self.selected_day.get()

        if language not in self.completed_quests:
            self.completed_quests[language] = []

        if day in self.completed_quests[language]:
            self.completed_quests[language].remove(day)
        else:
            self.completed_quests[language].append(day)

        self.save_progress()
        self.update_quest_display()
        self.update_day_button_colors()

    # Timer methods
    def toggle_timer(self):
        """Start or stop the timer"""
        if self.timer_running:
            self.stop_timer()
        else:
            self.start_timer()

    def start_timer(self):
        """Start the timer"""
        self.timer_running = True
        self.start_timer_btn.configure(text="⏸️")
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()

    def stop_timer(self):
        """Stop the timer"""
        self.timer_running = False
        self.start_timer_btn.configure(text="▶️")

    def reset_timer(self):
        """Reset the timer"""
        self.timer_running = False
        self.timer_seconds = 0
        self.start_timer_btn.configure(text="▶️")
        self.update_timer_display()

    def timer_worker(self):
        """Timer background worker"""
        while self.timer_running:
            time.sleep(1)
            if self.timer_running:
                self.timer_seconds += 1

    def update_timer_display(self):
        """Update timer and clock display"""
        # Update timer
        minutes = self.timer_seconds // 60
        seconds = self.timer_seconds % 60
        timer_text = f"Timer {minutes:02d}:{seconds:02d}"
        self.timer_label.configure(text=timer_text)

        # Update clock
        current_time = time.strftime("%H:%M")
        self.clock_label.configure(text=f"Uhr {current_time}")

        # Schedule next update
        self.root.after(1000, self.update_timer_display)

    # Button action methods
    def open_settings(self):
        """Open settings dialog"""
        settings_window = ctk.CTkToplevel(self.root)
        settings_window.title("Einstellungen")
        settings_window.geometry("400x300")
        settings_window.transient(self.root)

        # Settings content
        ctk.CTkLabel(
            settings_window,
            text="⚙️ Einstellungen",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=20)

        # Theme selection
        theme_frame = ctk.CTkFrame(settings_window)
        theme_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(theme_frame, text="Theme:").pack(pady=10)

        theme_var = ctk.StringVar(value="dark")
        theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            variable=theme_var,
            values=["dark", "light", "system"],
            command=lambda x: ctk.set_appearance_mode(x)
        )
        theme_menu.pack(pady=10)

    def open_editor(self):
        """Open code editor"""
        editor_window = ctk.CTkToplevel(self.root)
        editor_window.title("Code Editor")
        editor_window.geometry("800x600")
        editor_window.transient(self.root)

        # Editor content
        ctk.CTkLabel(
            editor_window,
            text="📝 Code Editor",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=10)

        # Text editor
        editor_text = ctk.CTkTextbox(
            editor_window,
            font=ctk.CTkFont(family="Courier", size=12)
        )
        editor_text.pack(fill="both", expand=True, padx=20, pady=20)

        # Insert current quest code example
        language = self.selected_language.get()
        day = self.selected_day.get()
        quest = self.quest_data[language][day]
        if "code_example" in quest:
            editor_text.insert("1.0", quest["code_example"])

    def open_youtube_links(self):
        """Open YouTube links for current quest"""
        language = self.selected_language.get()
        day = self.selected_day.get()
        quest = self.quest_data[language][day]

        if "youtube_links" in quest:
            for link in quest["youtube_links"]:
                webbrowser.open(link)
        else:
            # Open general programming tutorial search
            search_query = f"{language} programming tutorial"
            webbrowser.open(f"https://www.youtube.com/results?search_query={search_query}")

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CodeTrainingApp()
    app.run()
