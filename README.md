# 🚀 Code Training App - 60 Days Challenge

Eine umfassende Desktop-Anwendung zum Erlernen von Programmiersprachen in 60 Tagen, erstellt mit CustomTkinter.

## ✨ Features

### 🎯 Hauptfunktionen
- **12 Programmiersprachen**: Python, JavaScript, Java, C++, C#, Go, Rust, TypeScript, PHP, Ruby, Swift, Kotlin
- **60 Tage Lernplan**: Strukturierte Lernreise von Anfänger bis Fortgeschritten
- **Interaktive Benutzeroberfläche**: Modernes Design mit dunklem Theme
- **Fortschrittsverfolgung**: Automatisches Speichern des Lernfortschritts
- **Timer-Funktion**: Integrierter Timer für Lerneinheiten
- **Code-Editor**: Eingebauter Editor zum Testen von Code
- **YouTube Integration**: Direkte Links zu Lernvideos

### 🖥️ Benutzeroberfläche
- **Header**: Timer und Uhrzeit-Anzeige
- **Seitenleiste**: Sprachauswahl und Tag-Navigation (1-60)
- **Hauptbereich**: Detaillierte Quest-Beschreibungen mit Code-Beispielen
- **Footer**: Einstellungen, Editor und YouTube-Links

### 📚 Lerninhalt
Jeder Tag enthält:
- **Lernziele**: Klare Objectives für den Tag
- **Beschreibung**: Detaillierte Erklärung der Konzepte
- **Code-Beispiele**: Praktische Programmierbeispiele
- **Aufgaben**: Hands-on Übungen zum Vertiefen
- **Schwierigkeitsgrad**: Beginner (🟢), Intermediate (🟡), Advanced (🔴)

## 🚀 Installation

### Voraussetzungen
- Python 3.7 oder höher
- pip (Python Package Manager)

### Setup
1. **Repository klonen oder Dateien herunterladen**
   ```bash
   # Falls Git verwendet wird
   git clone <repository-url>
   cd code-training-app
   ```

2. **Dependencies installieren**
   ```bash
   pip install customtkinter
   ```

3. **Anwendung starten**
   ```bash
   python code_training_app.py
   ```

## 🎮 Verwendung

### Erste Schritte
1. **Sprache auswählen**: Wähle deine gewünschte Programmiersprache aus der Dropdown-Liste
2. **Tag auswählen**: Klicke auf einen Tag (1-60) in der Seitenleiste
3. **Quest starten**: Lese die Beschreibung und Lernziele
4. **Timer starten**: Nutze den Timer um deine Lernzeit zu verfolgen
5. **Code testen**: Verwende den integrierten Editor
6. **Als abgeschlossen markieren**: Markiere den Tag als erledigt

### Timer-Funktionen
- **▶️ Start/Pause**: Timer starten oder pausieren
- **🔄 Reset**: Timer zurücksetzen
- **Automatische Anzeige**: Zeigt aktuelle Uhrzeit und Timer-Zeit

### Fortschrittsverfolgung
- **Grüne Tage**: Abgeschlossene Quests
- **Blaue Tage**: Aktuell ausgewählter Tag
- **Graue Tage**: Noch nicht bearbeitete Tage
- **Automatisches Speichern**: Fortschritt wird in `progress.json` gespeichert

### Zusätzliche Features
- **Settings**: Theme-Einstellungen (Dark/Light/System)
- **Code Editor**: Vollständiger Editor mit Syntax-Highlighting
- **YouTube Links**: Direkte Links zu relevanten Tutorials

## 📁 Projektstruktur

```
code-training-app/
├── code_training_app.py    # Hauptanwendung
├── progress.json          # Fortschrittsdaten (automatisch erstellt)
└── README.md             # Diese Datei
```

## 🛠️ Technische Details

### Verwendete Technologien
- **CustomTkinter**: Moderne GUI-Bibliothek für Python
- **Threading**: Für Timer-Funktionalität
- **JSON**: Für Datenpersistierung
- **Webbrowser**: Für YouTube-Integration

### Architektur
- **Objektorientiert**: Saubere Klassenstruktur
- **Modular**: Getrennte Methoden für verschiedene Funktionen
- **Erweiterbar**: Einfach neue Sprachen und Quests hinzuzufügen

## 🎨 Anpassung

### Neue Sprachen hinzufügen
1. Sprache zur `self.languages` Liste hinzufügen
2. Neue Quest-Methode erstellen (z.B. `get_new_language_quests()`)
3. Methode in `initialize_comprehensive_quest_data()` registrieren

### Quest-Inhalte bearbeiten
- Bearbeite die entsprechenden `get_*_quests()` Methoden
- Jede Quest kann folgende Felder enthalten:
  - `title`: Titel der Quest
  - `description`: Beschreibung
  - `objectives`: Liste der Lernziele
  - `code_example`: Code-Beispiel
  - `exercise`: Aufgabenstellung
  - `youtube_links`: Liste von YouTube-URLs

## 🤝 Beitragen

Verbesserungen und neue Features sind willkommen! 

### Mögliche Erweiterungen
- Mehr Programmiersprachen
- Detailliertere Quest-Inhalte
- Syntax-Highlighting im Code-Editor
- Export-Funktionen für Fortschritt
- Gamification-Elemente
- Online-Synchronisation

## 📄 Lizenz

Dieses Projekt steht unter der MIT-Lizenz - siehe LICENSE Datei für Details.

## 🙏 Danksagungen

- **CustomTkinter**: Für die moderne GUI-Bibliothek
- **Python Community**: Für die großartige Sprache und Ecosystem
- **Open Source**: Für die Inspiration und Tools

---

**Viel Erfolg beim Programmieren lernen! 🎉**
